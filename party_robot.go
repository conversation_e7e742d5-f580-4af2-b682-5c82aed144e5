package partyrobot

import (
	"strconv"
	"strings"
)

// Welcome greets a person by name.
func Welcome(name string) string {
	return "Welcome to my party, " + name + "!"
}

// HappyBirthday wishes happy birthday to the birthday person and exclaims their age.
func HappyBirthday(name string, age int) string {
	return "Happy birthday " + name + "! You are now " + string(age) + " years old!"
}

// AssignTable assigns a table to each guest.
func AssignTable(name string, table int, neighbor, direction string, distance float64) string {
	_table := string(table)
	_distance := strconv.FormatFloat(distance, 'f', 2, 64) 
	length := len(_table)
	if length < 3 {
		_table = strings.Repeat("0", 3-length) + _table
	}
	
	return "Welcome to my party, " + name + "!\nYou have been assigned to table " + _table + ". Your table is " + direction + ", exactly " + _distance + " meters from here.\nYou will be sitting next to " + neighbor + "."
}
